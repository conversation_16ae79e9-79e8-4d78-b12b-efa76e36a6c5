package com.bolt.dashboard.service;

import java.util.Date;
import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.ConstantVariable;
import com.bolt.dashboard.core.model.BuildFailurePatternForProjectInJenkinsModel;
import com.bolt.dashboard.core.model.ConfigurationSetting;
import com.bolt.dashboard.core.model.ConfigurationToolInfoMetric;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.core.repository.BuildFailurePatternForProjectRepo;
import com.bolt.dashboard.core.repository.BuildToolRep;
import com.bolt.dashboard.core.repository.ChartConfigRepo;
import com.bolt.dashboard.core.repository.CodeCoverageRepository;
import com.bolt.dashboard.core.repository.CodeQualityRep;
import com.bolt.dashboard.core.repository.ConfigurationSettingRep;
import com.bolt.dashboard.core.repository.GoalSettingRep;
import com.bolt.dashboard.core.repository.HealthDataRepo;
import com.bolt.dashboard.core.repository.PortfolioConfigRepo;
import com.bolt.dashboard.core.repository.ProjectHealthRep;
import com.bolt.dashboard.core.repository.SCMToolRepository;
import com.bolt.dashboard.core.repository.UserAssociationRep;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.util.EncryptionDecryptionAES;

@Service
public class ConfigurationSettingServiceImplementation implements ConfigurationSettingService {
    private ConfigurationSettingRep configurationSettingRepository;
    private AlmService almService;
    private BuildToolRep buildRepo;
    private BuildFailurePatternForProjectRepo buildFailurePatternRepo;
    private CodeCoverageRepository codeCoverageRepo;
    private CodeQualityRep codeQualityRep;
    private HealthDataRepo healthRepo;
    private SCMToolRepository scmRepo;
    private ALMConfigRepo almConfigRepo;
    private ChartConfigRepo chartConfigRepo;
    private GoalSettingRep goalSettingRepo;
    private PortfolioConfigRepo portfolioRepo;
    private ProjectHealthRep projectHealthRepo;
    private UserAssociationRep userAssociation;
    @Autowired
    public ConfigurationSettingServiceImplementation(ConfigurationSettingRep configurationSettingRepository,
			AlmService almService, BuildToolRep buildRepo, BuildFailurePatternForProjectRepo buildFailurePatternRepo,
			CodeCoverageRepository codeCoverageRepo, CodeQualityRep codeQualityRep, HealthDataRepo healthRepo,
			SCMToolRepository scmRepo, ALMConfigRepo almConfigRepo, ChartConfigRepo chartConfigRepo,
			GoalSettingRep goalSettingRepo, PortfolioConfigRepo portfolioRepo, ProjectHealthRep projectHealthRepo,
			UserAssociationRep userAssociation) {
		super();
		this.configurationSettingRepository = configurationSettingRepository;
		this.almService = almService;
		this.buildRepo = buildRepo;
		this.buildFailurePatternRepo = buildFailurePatternRepo;
		this.codeCoverageRepo = codeCoverageRepo;
		this.codeQualityRep = codeQualityRep;
		this.healthRepo = healthRepo;
		this.scmRepo = scmRepo;
		this.almConfigRepo = almConfigRepo;
		this.chartConfigRepo = chartConfigRepo;
		this.goalSettingRepo = goalSettingRepo;
		this.portfolioRepo = portfolioRepo;
		this.projectHealthRepo = projectHealthRepo;
		this.userAssociation = userAssociation;
	}

	
    
	private static final Logger LOG = LogManager.getLogger(ConfigurationSettingServiceImplementation.class);

    
//    public ConfigurationSettingServiceImplementation(ConfigurationSettingRep configurationSettingRepository,AlmService almService,BuildToolRep buildRepo,
//    		BuildFailurePatternForProjectRepo buildFailurePatternRepo,CodeCoverageRepository codeCoverageRepo,
//    		CodeQualityRep codeQualityRep,HealthDataRepo healthRepo,
//    		SCMToolRepository scmRepo) {
//        this.configurationSettingRepository = configurationSettingRepository;
//    }

    @Override
//    @Cacheable(value="getConfig", key ="'getConfig'", cacheManager="timeoutCacheManager")
    public DataResponse<Iterable<ConfigurationSetting>> getConfig() {
        long lastUpdated = 1;
        Iterable<ConfigurationSetting> result = configurationSettingRepository.findAll();

        return new DataResponse<Iterable<ConfigurationSetting>>(result, lastUpdated);
    }

    @Override
    public ConfigurationSetting addConfig(ConfigurationSetting req) {
        Date date = new Date();
        deleteConfig(req);
        long timeStamp = date.getTime();
        req.setTimestamp((long) timeStamp);
        req.setProjectName(req.getProjectName());
        if (configurationSettingRepository.findByProjectName(req.getProjectName()) != null) {
            configurationSettingRepository.deleteByProjectName(req.getProjectName());
        }
        return configurationSettingRepository.save(req);
    }

    @Override
    public int deleteConfig(ConfigurationSetting configurationSetting) {
        LOG.info("deleted");
        return configurationSettingRepository.deleteByProjectName(configurationSetting.getProjectName());
    }

	@Override
	public boolean deleteAllCollections(String projectName) {
		
		almService.delAllIssues(projectName);
		buildRepo.delete(buildRepo.findByName(projectName));
		List<BuildFailurePatternForProjectInJenkinsModel> failurePattern=buildFailurePatternRepo.findByProjectName(projectName);
		if(!failurePattern.isEmpty()) {
			BuildFailurePatternForProjectInJenkinsModel failurePatternObj=failurePattern.get(0);
		failurePatternObj.getPatternMetrics().forEach((value)->{
			value.setPatternCount(0);
		});
		buildFailurePatternRepo.save(failurePatternObj);
		}
		codeCoverageRepo.delete(codeCoverageRepo.findByProjectName(projectName));
		codeQualityRep.delete(codeQualityRep.findByName(projectName));
		healthRepo.delete(healthRepo.findByProjectName(projectName));
		scmRepo.delete(scmRepo.findByProjectName(projectName));
		return true;
	}

	@Override
	public boolean deleteProject(String projectName) {
		
		deleteAllCollections(projectName);
		almConfigRepo.delete(almConfigRepo.findByProjectName(projectName));
		buildFailurePatternRepo.delete(buildFailurePatternRepo.findByProjectName(projectName));
		goalSettingRepo.delete(goalSettingRepo.findByProjectName(projectName));
		portfolioRepo.delete(portfolioRepo.findByProjectName(projectName));
		projectHealthRepo.delete(projectHealthRepo.findByProjectName(projectName));
		userAssociation.delete(userAssociation.findByPName(projectName));
		chartConfigRepo.delete(chartConfigRepo.findByPName(projectName));
		configurationSettingRepository.delete(configurationSettingRepository.findByProjectName(projectName));
		
		return false;
	}

	@Override
//	@Cacheable(value="getConfigProject", key ="'getConfigProject'+#pName", cacheManager="timeoutCacheManager")
	public ConfigurationSetting getConfigProject(String pName) {
		
		
		ConfigurationSetting config=configurationSettingRepository.findByProjectName(pName).get(0);
		if(config.getMetrics()!=null)
		for(ConfigurationToolInfoMetric metric: config.getMetrics()) {
			metric.setPassword(EncryptionDecryptionAES.decrypt(metric.getPassword(), ConstantVariable.SECRET_KEY));
		  }
				
				return config;
	}

}
