## Current Challenges & Limitations

### **Variable Loop Detection Issues**
**Challenge**: Variables inside for loops are not properly eliminated or tracked

**Examples**:
```java
// Challenge: Loop variables not properly filtered
for (int i = 0; i < list.size(); i++) {
    String item = list.get(i);  // 'item' should be temporary
    processItem(item);
}

// Challenge: Iterator variables persist
for (User user : userList) {  // 'user' should be scoped to loop
    user.setActive(true);
}
```

**Impact**: 
- Creates noise in variable tracking
- False positive relationships
- Cluttered Neo4j visualization

**Current Mitigation**: Basic filtering in LLM prompts, but not comprehensive

### **Incomplete Variable Transformation Capture**
**Challenge**: Not able to fully capture variable transformations and operations

**Examples**:
```java
// Challenge: Complex transformations not captured
String rawData = getData();
String cleanData = rawData.trim().toLowerCase();  // Transformation lost
String processedData = cleanData.replace(" ", "_");  // Chain not tracked

// Challenge: Method call transformations
User user = userService.findById(id);
UserDTO dto = userMapper.toDTO(user);  // Mapping relationship missed
return dto;

// Challenge: Collection operations
List<String> names = users.stream()
    .map(User::getName)  // Stream operation not tracked
    .filter(name -> name.length() > 3)  // Filter logic lost
    .collect(Collectors.toList());
```

**Impact**:
- Incomplete data lineage
- Missing transformation steps
- Reduced traceability

### **Complex Expression Parsing**
**Challenge**: Difficulty parsing complex expressions and nested operations

**Examples**:
```java
// Challenge: Nested method calls
result = service.process(
    mapper.transform(
        validator.validate(inputData)  // Nested relationships lost
    )
);

// Challenge: Conditional assignments
String value = condition ? getValue1() : getValue2();  // Branching not captured

// Challenge: Lambda expressions
users.forEach(user -> {
    user.setLastLogin(new Date());  // Lambda scope not tracked
    auditService.log(user.getId());
});

// Challenge: Builder patterns
User user = User.builder()
    .name("John")           // Builder step not tracked
    .email("<EMAIL>") // Chain relationship lost
    .build();               // Final transformation missed

// Challenge: Stream operations with complex chains
List<UserDTO> result = users.stream()
    .filter(u -> u.isActive())                    // Filter logic not captured
    .map(u -> userMapper.toDTO(u))               // Mapping step lost
    .sorted(Comparator.comparing(UserDTO::getName)) // Sort operation ignored
    .collect(Collectors.toList());               // Collection not tracked
```

**Impact**:
- **Incomplete Lineage**: Missing intermediate transformation steps
- **Lost Context**: Complex operations appear as simple assignments
- **Reduced Traceability**: Cannot follow data through complex pipelines

### **AST Parsing Limitations**
**Challenge**: AST parsing doesn't capture all semantic relationships

**Examples**:
```java
// Challenge: Dynamic method calls
String methodName = "process" + type;
Method method = clazz.getMethod(methodName);  // Dynamic call not tracked

// Challenge: Annotation-driven relationships
@Autowired
private UserService userService;  // Dependency injection not captured

// Challenge: Configuration-based relationships
@Value("${app.config.timeout}")
private int timeout;  // Configuration relationship missed
```

