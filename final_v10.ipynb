# ========== COMBINED DATA LINEAGE PIPELINE V11 ==========
# Combines best features from final_v9.ipynb and final_v10.ipynb
# Based on user preferences: 3-stage pipeline with consolidated LLM processing

# FIXED: Add retry logic for rate limiting
import time
from tenacity import retry, stop_after_attempt, wait_exponential

# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========
import os
import json
import re
import uuid
from pathlib import Path
from tqdm import tqdm
import pandas as pd
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Tree-sitter for AST parsing
from tree_sitter import Language, Parser
import tree_sitter_java as tsjava

# LangChain components
from langchain_community.document_loaders import TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter, Language as LC_Language
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_community.graphs import Neo4jGraph
from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import PromptTemplate
from langchain.schema import Document

# Configuration - Update path as needed
BASE_PATH = Path(r"C:/Shaik/sample/ServicesBolt")

# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "Test@7889"
NEO4J_DB = "servicesbolt"

# Initialize connections
graph = Neo4jGraph(url=NEO4J_URI, username=NEO4J_USER, password=NEO4J_PASSWORD, database=NEO4J_DB)
JAVA_LANGUAGE = Language(tsjava.language())
parser = Parser(JAVA_LANGUAGE)

# Azure OpenAI Configuration
llm = AzureChatOpenAI(
    api_key="********************************",
    azure_endpoint="https://azureopenaibrsc.openai.azure.com/",
    azure_deployment="gpt-4o",
    api_version="2024-12-01-preview"
)

# Temp variables to filter out (User Preference from v9)
TEMP_VARIABLES = {
    'i', 'j', 'k', 'l', 'm', 'n', 'x', 'y', 'z',
    'temp', 'tmp', 'temporary', 'temp1', 'temp2',
    'count', 'counter', 'index', 'idx', 'iter',
    'result', 'res', 'ret', 'val', 'value',
    'item', 'elem', 'element', 'obj', 'object',
    'str', 'string', 'num', 'number', 'flag',
    'bool', 'boolean', 'arr', 'array', 'list',
    'map', 'set', 'data', 'info', 'param', 'arg','Status'
}

def remove_java_comments(source_code):
    """Remove all types of Java comments to avoid false positives in pattern matching"""
    # Remove single-line comments
    source_code = re.sub(r'//.*$', '', source_code, flags=re.MULTILINE)
    # Remove multi-line comments including /** */ blocks
    source_code = re.sub(r'/\*.*?\*/', '', source_code, flags=re.DOTALL)
    return source_code

# Long-term memory storage
MEMORY_FILE = "servicesbolt_memory_v11.json"
memory_lock = threading.Lock()

def load_memory():
    """Load long-term memory from disk"""
    # Try to load JSON file first
    try:
        with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            # Convert lists back to sets where needed
            def convert_from_json(obj):
                if isinstance(obj, dict):
                    result = {}
                    for k, v in obj.items():
                        if k == 'validated_edges' and isinstance(v, list):
                            result[k] = set(v)
                        else:
                            result[k] = convert_from_json(v)
                    return result
                elif isinstance(obj, list):
                    return [convert_from_json(item) for item in obj]
                else:
                    return obj
            
            return convert_from_json(data)
            
    except FileNotFoundError:
        # Try to load old pickle file if JSON doesn't exist
        old_pickle_file = MEMORY_FILE.replace('.json', '.pkl')
        try:
            import pickle
            with open(old_pickle_file, 'rb') as f:
                data = pickle.load(f)
                print(f"📦 Loaded memory from old pickle file: {old_pickle_file}")
                print(f"🔄 Converting to JSON format...")
                # Save as JSON for future use
                save_memory(data)
                return data
        except FileNotFoundError:
            pass
        
        # Return default memory structure (enhanced from both versions)
        return {
            'class_registry': {},
            'dto_mappings': {},
            'validated_edges': set(),
            'code_index': {},
            'variable_flows': {},
            'method_signatures': {},
            'transformation_cache': {},
            'variable_contexts': {}  # Enhanced variable context tracking
        }
    except json.JSONDecodeError as e:
        print(f"⚠️ Error loading JSON memory file: {e}")
        print(f"🔄 Returning default memory structure")
        return {
            'class_registry': {},
            'dto_mappings': {},
            'validated_edges': set(),
            'code_index': {},
            'variable_flows': {},
            'method_signatures': {},
            'transformation_cache': {},
            'variable_contexts': {}  # Enhanced variable context tracking
        }

def save_memory(memory):
    """Save long-term memory to disk"""
    with memory_lock:
        try:
            # Create a copy to avoid modifying the original
            memory_copy = memory.copy()
            
            # Convert sets to lists for JSON serialization
            def convert_for_json(obj):
                if isinstance(obj, set):
                    return list(obj)
                elif isinstance(obj, dict):
                    return {k: convert_for_json(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_for_json(item) for item in obj]
                else:
                    return obj
            
            memory_copy = convert_for_json(memory_copy)
            
            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(memory_copy, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ Error saving memory to JSON: {e}")
            # Fallback: save as pickle if JSON fails
            backup_file = MEMORY_FILE.replace('.json', '_backup.pkl')
            import pickle
            with open(backup_file, 'wb') as f:
                pickle.dump(memory, f)
            print(f"💾 Memory saved as backup pickle file: {backup_file}")

# Initialize memory
memory = load_memory()

# Clear Neo4j database
graph.query("MATCH (n) DETACH DELETE n")
print("✅ Stage 1 Complete: Configuration loaded and Neo4j cleared")

# ========== RETRY LOGIC FOR RATE LIMITING ==========
@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=60))
def process_with_retry(transformer, doc):
    """Process LLM request with retry logic for rate limiting"""
    try:
        return transformer.convert_to_graph_documents([doc])
    except Exception as e:
        error_str = str(e).lower()
        if "429" in error_str or "rate limit" in error_str or "quota" in error_str:
            print(f"🔄 Rate limit hit, retrying after delay...")
            raise  # This will trigger the retry
        else:
            print(f"❌ Non-rate-limit error: {e}")
            raise  # Re-raise non-rate-limit errors

print("✅ Rate limiting retry logic loaded")

# ========== IMPROVED UTILITY FUNCTIONS (COMBINED FROM V9 & V10) ==========

def to_pascal_case(text):
    """Convert text to PascalCase with improved handling"""
    if not text:
        return text
    
    # Remove file extensions first
    text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # Handle file paths - extract just the filename
    if '/' in text or '\\' in text:
        text = os.path.basename(text)
        text = re.sub(r'\.(java|class)$', '', text, flags=re.IGNORECASE)
    
    # If already in PascalCase, return as is
    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):
        return text
    
    # Handle camelCase to PascalCase conversion
    if re.match(r'^[a-z][a-zA-Z0-9]*$', text):
        return text[0].upper() + text[1:]
    
    # Split on common delimiters and capitalize each part
    parts = re.split(r'[_\-\s]+', text)
    result = ''
    for part in parts:
        if part:
            result += part[0].upper() + part[1:].lower() if len(part) > 1 else part.upper()
    
    return result if result else text

def extract_clean_name(full_name, name_type):
    """Extract clean name from potentially concatenated strings"""
    if not full_name:
        return full_name
    
    # Remove common prefixes
    prefixes = ['method:', 'class:', 'variable:', 'field:', 'table:', 'endpoint:']
    for prefix in prefixes:
        if full_name.lower().startswith(prefix):
            full_name = full_name[len(prefix):]
    
    # Remove file extensions EXCEPT for file type (preserve .java for files to distinguish from classes)
    if name_type.lower() != 'file':
        full_name = re.sub(r'\.(java|class)$', '', full_name, flags=re.IGNORECASE)
    
    # Handle file.class patterns - extract only class name
    if '.' in full_name and name_type.lower() in ['class', 'interface']:
        parts = full_name.split('.')
        # Take the last part as the class name
        full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Handle classname:method or classname.method patterns - extract only method name
    if name_type.lower() == 'method':
        # Handle both colon and dot separators
        if ':' in full_name:
            parts = full_name.split(':')
            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
        elif '.' in full_name:
            parts = full_name.split('.')
            full_name = parts[-1] if parts[-1] else parts[-2] if len(parts) > 1 else full_name
    
    # Apply PascalCase for classes, methods, files, folders
    if name_type.lower() in ['class', 'interface', 'method', 'file', 'folder']:
        return to_pascal_case(full_name)
    
    # For variables, keep original name (context handled separately)
    if name_type.lower() == 'variable':
        if '.' in full_name:
            return full_name.split('.')[-1]  # Return only variable name
        return full_name
    
    # For tables, apply PascalCase
    if name_type.lower() == 'table':
        return to_pascal_case(full_name)
    
    return full_name

def is_temp_variable(var_name):
    """Check if variable is a common temp variable (from v9 preferences)"""
    if not var_name:
        return True
    var_lower = var_name.lower().strip()
    return var_lower in TEMP_VARIABLES or len(var_lower) <= 1

def get_variable_context(var_name, method_name=None, class_name=None):
    """Get context for a variable (method or class where it's defined)"""
    if method_name:
        return extract_clean_name(method_name, 'method')
    elif class_name:
        return extract_clean_name(class_name, 'class')
    return None

print("✅ Improved utility functions loaded")

# ========== IMPROVED VARIABLE METADATA REGISTRY ==========

class ImprovedVariableRegistry:
    """Enhanced registry to track variables with proper context separation"""
    
    def __init__(self):
        self.variables = {}  # var_id -> metadata
        self.name_to_id = {}  # (var_name, context) -> var_id
        self.chunk_memory = {}  # chunk_id -> variables seen
        
    def register_variable(self, var_name, context, chunk_id, context_info):
        """Register a variable with unique ID and context metadata"""
        # Clean variable name
        clean_var_name = extract_clean_name(var_name, 'variable')
        clean_context = extract_clean_name(context, context_info.get('context_type', 'method'))
        
        # Create unique key
        var_key = (clean_var_name, clean_context)
        
        if var_key in self.name_to_id:
            var_id = self.name_to_id[var_key]
            self.variables[var_id]['chunks'].add(chunk_id)
            self.variables[var_id]['contexts'].append(context_info)
        else:
            var_id = f"var_{uuid.uuid4().hex[:8]}"
            self.name_to_id[var_key] = var_id
            self.variables[var_id] = {
                'variable_name': clean_var_name,  # Only variable name for display
                'context_name': clean_context,    # Method/class context
                'context_type': context_info.get('context_type', 'method'),
                'chunks': {chunk_id},
                'contexts': [context_info],
                'declared_in': chunk_id if context_info.get('action') == 'declared' else None,
                'modifications': [],
                'usages': [],
                'data_type': context_info.get('data_type'),
                'lineage_path': []
            }
        
        if chunk_id not in self.chunk_memory:
            self.chunk_memory[chunk_id] = set()
        self.chunk_memory[chunk_id].add(var_id)
        
        return var_id
    
    def get_variable_for_neo4j(self, var_id):
        """Get variable data formatted for Neo4j"""
        if var_id in self.variables:
            var_data = self.variables[var_id]
            return {
                'name': var_data['variable_name'],  # Display name only
                'context': var_data['context_name'], # Context for uniqueness
                'context_type': var_data['context_type'],
                'full_context': f"{var_data['context_name']}.{var_data['variable_name']}"
            }
        return None

def create_unique_variable_name(var_name, context_name, context_type='method'):
    """Create unique variable name with context for proper relationship mapping"""
    if not var_name or not context_name:
        return var_name
    
    # Clean names
    clean_var = extract_clean_name(var_name, 'variable')
    clean_context = extract_clean_name(context_name, context_type)
    
    # Create unique identifier: context.variable
    return f"{clean_context}.{clean_var}"

def is_method_name(name, class_registry):
    """Check if a name is actually a method name to prevent method-variable confusion"""
    if not name:
        return False
    
    # Check if name ends with () - likely a method call
    if name.endswith('()'):
        return True
    
    # Check against known method signatures in class registry
    for class_name, class_info in class_registry.items():
        if 'source_code' in class_info:
            # Simple regex to find method declarations
            import re
            method_pattern = rf'\b(public|private|protected)\s+\w+\s+{re.escape(name)}\s*\('
            if re.search(method_pattern, class_info['source_code']):
                return True
    
    return False

# Initialize improved variable registry
variable_registry = ImprovedVariableRegistry()

print(" Improved Variable Registry initialized")

# ========== MEMORY OPTIMIZATION FOR LARGE CODEBASES - FIXED ==========

def process_classes_in_batches(class_registry, batch_size=10):
    """Process classes in smaller batches to prevent memory issues - FIXED"""
    class_names = list(class_registry.keys())
    
    print(f"🔧 Processing {len(class_names)} classes in batches of {batch_size}")
    
    for i in range(0, len(class_names), batch_size):
        batch = class_names[i:i+batch_size]
        
        print(f"   📦 Processing batch {i//batch_size + 1}: {len(batch)} classes")
        
        # Create isolated memory context for this batch
        batch_memory = {
            'variable_contexts': {},
            'method_signatures': {},
            'current_batch_edges': set()
        }
        
        yield batch, batch_memory
        
        # Clean up after batch
        del batch_memory
        import gc
        gc.collect()

def optimize_memory_usage():
    """Implement memory optimization strategies - FIXED"""
    import gc
    
    # Force garbage collection
    gc.collect()
    
    # Clear unnecessary variables from memory
    print("🧹 Memory optimization complete")

print("\n🔧 Memory Optimization for Large Codebases implemented!")
print("📝 Key Features:")
print("   📦 Batch processing to prevent memory overload")
print("   🧹 Automatic garbage collection after each batch")
print("   🔄 Isolated memory contexts for each batch")
print("   ⚡ Optimized for codebases with 100k+ lines and 1000+ classes")

# ========== ENHANCED VARIABLE CONTEXT TRACKING ==========

class EnhancedVariableContextTracker:
    """Enhanced variable context tracker that distinguishes between global and local variables"""
    
    def __init__(self):
        self.variable_registry = {}  # var_id -> full metadata
        self.global_variables = {}   # class_name -> {var_name: var_id}
        self.local_variables = {}    # method_name -> {var_name: var_id}
        self.variable_contexts = {}  # var_name -> context info
    
    def register_global_variable(self, var_name, class_name, file_path=None):
        """Register a global variable (class field)"""
        clean_var = extract_clean_name(var_name, 'variable')
        clean_class = extract_clean_name(class_name, 'class')
        
        var_id = f"global_{clean_class}_{clean_var}_{uuid.uuid4().hex[:6]}"
        
        # Store in global variables registry
        if clean_class not in self.global_variables:
            self.global_variables[clean_class] = {}
        self.global_variables[clean_class][clean_var] = var_id
        
        # Store full metadata
        self.variable_registry[var_id] = {
            'variable_name': clean_var,
            'context_name': clean_class,
            'context_type': 'class',
            'scope': 'global',
            'full_name': f"{clean_class}.{clean_var}",
            'file_path': file_path
        }
        
        return var_id
    
    def register_local_variable(self, var_name, method_name, class_name, file_path=None):
        """Register a local variable (method variable)"""
        clean_var = extract_clean_name(var_name, 'variable')
        clean_method = extract_clean_name(method_name, 'method')
        clean_class = extract_clean_name(class_name, 'class')
        
        var_id = f"local_{clean_method}_{clean_var}_{uuid.uuid4().hex[:6]}"
        
        # Store in local variables registry
        method_key = f"{clean_class}.{clean_method}"
        if method_key not in self.local_variables:
            self.local_variables[method_key] = {}
        self.local_variables[method_key][clean_var] = var_id
        
        # Store full metadata
        self.variable_registry[var_id] = {
            'variable_name': clean_var,
            'context_name': clean_method,
            'context_type': 'method',
            'scope': 'local',
            'parent_class': clean_class,
            'full_name': f"{clean_method}.{clean_var}",
            'file_path': file_path
        }
        
        return var_id
    
    def get_variable_for_neo4j(self, var_id):
        """Get variable data formatted for Neo4j with proper context"""
        if var_id in self.variable_registry:
            var_data = self.variable_registry[var_id]
            return {
                'name': var_data['variable_name'],  # Display only variable name
                'context': var_data['context_name'], # Class name for global, Method name for local
                'context_type': var_data['context_type'], # 'class' or 'method'
                'scope': var_data['scope'], # 'global' or 'local'
                'full_name': var_data['full_name']
            }
        return None

def enhance_variable_context_tracking():
    """Enhance existing variable tracking with proper global/local distinction"""
    print("🔧 Enhancing variable context tracking with global/local distinction...")
    
    # Initialize enhanced tracker
    enhanced_tracker = EnhancedVariableContextTracker()
    
    print(f"\n✅ Enhanced variable context tracking complete")
    
    return enhanced_tracker

print("\n🔧 Enhanced Variable Context Tracking implemented!")
print("📝 Key Features:")
print("   🌐 Global variables (class fields) → Context: ClassName")
print("   🔧 Local variables (method variables) → Context: MethodName")
print("   📊 Variable IDs for unique tracking using memory system")
print("   🎯 Neo4j display: Variable name only, context as metadata")
print("\n📝 Usage:")
print("   enhanced_tracker = enhance_variable_context_tracking()")
print("\n🎯 Neo4j Result:")
print("   Global: name='userId', context='UserService', context_type='class', scope='global'")
print("   Local:  name='result', context='processData', context_type='method', scope='local'")

# ========== STAGE 2: FOLDER-FILE HIERARCHY & FILE-CLASS RELATIONSHIPS ==========
# Combined from v9 and v10 - includes both folder-file and file-class relationships

def extract_folder_file_hierarchy():
    """Extract and normalize folder-file relationships with improved naming"""
    relationships = []
    base_folder = to_pascal_case(BASE_PATH.name)
    
    # Track unique folders and files
    folders = set()
    files = set()
    
    # Process all Java files
    for java_file in BASE_PATH.rglob("*.java"):
        file_name = to_pascal_case(java_file.stem)  # Remove .java extension and convert to PascalCase
        files.add(file_name)
        
        # Get parent folder
        parent_folder = java_file.parent
        
        # If file is directly in BASE_PATH, parent is the base folder
        if parent_folder == BASE_PATH:
            parent_name = base_folder
        else:
            parent_name = to_pascal_case(parent_folder.name)
            folders.add(parent_name)
            
            # Add folder hierarchy if needed
            current_folder = parent_folder
            while current_folder.parent != BASE_PATH and current_folder.parent != current_folder:
                grandparent_name = to_pascal_case(current_folder.parent.name)
                folders.add(grandparent_name)
                
                # Add folder-to-folder relationship
                relationships.append({
                    'source_node': grandparent_name,
                    'source_type': 'Folder',
                    'destination_node': to_pascal_case(current_folder.name),
                    'destination_type': 'Folder',
                    'relationship': 'CONTAINS',
                    'parent': grandparent_name
                })
                
                current_folder = current_folder.parent
        
        # Add folder-to-file relationship
        relationships.append({
            'source_node': parent_name,
            'source_type': 'Folder',
            'destination_node': file_name,
            'destination_type': 'File',
            'relationship': 'CONTAINS',
            'parent': parent_name
        })
    
    return relationships, len(folders), len(files)

def extract_file_class_relationships_ast():
    """Extract file-class relationships using AST parsing (from v10)"""
    file_class_relationships = []
    
    for java_file in BASE_PATH.rglob("*.java"):
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # Parse with tree-sitter
            tree = parser.parse(source_code.encode('utf-8'))
            root_node = tree.root_node
            
            file_name = to_pascal_case(java_file.stem)
            
            # Find class declarations
            def find_classes(node):
                classes = []
                if node.type in ['class_declaration', 'interface_declaration', 'enum_declaration']:
                    for child in node.children:
                        if child.type == 'identifier':
                            class_name = to_pascal_case(source_code[child.start_byte:child.end_byte])
                            classes.append(class_name)
                            break
                
                for child in node.children:
                    classes.extend(find_classes(child))
                
                return classes
            
            classes = find_classes(root_node)
            
            # Create file-class relationships
            for class_name in classes:
                file_class_relationships.append({
                    'source_node': file_name,
                    'source_type': 'File',
                    'destination_node': class_name,
                    'destination_type': 'Class',
                    'relationship': 'DECLARES',
                    'parent': file_name
                })
                
        except Exception as e:
            print(f"⚠️ Error processing {java_file}: {e}")
            continue
    
    return file_class_relationships

# Execute Stage 2: Folder-File Hierarchy
hierarchy_relationships, folder_count, file_count = extract_folder_file_hierarchy()
df_hierarchy = pd.DataFrame(hierarchy_relationships)

# Execute Stage 2B: File-Class Relationships
file_class_relationships = extract_file_class_relationships_ast()
df_file_class = pd.DataFrame(file_class_relationships)

# Combine both relationship types
if len(df_file_class) > 0:
    df_hierarchy = pd.concat([df_hierarchy, df_file_class], ignore_index=True)
    print(f"📁 Added {len(df_file_class)} file-class relationships")

# Store results in memory
memory['stage_2_results'] = {
    'folders': folder_count,
    'files': file_count,
    'file_class_relationships': len(df_file_class),
    'total_relationships': len(df_hierarchy)
}

# Add to validated edges
for _, row in df_hierarchy.iterrows():
    edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
    memory['validated_edges'].add(edge_key)
save_memory(memory)

print(f"✅ Stage 2 Complete: {len(df_hierarchy)} total relationships extracted")
print(f"📊 Folders: {folder_count}, Files: {file_count}, File-Class: {len(df_file_class)}")

# ========== STAGE 2B: AST-BASED FILE-CLASS RELATIONSHIPS ==========

def extract_file_class_relationships_ast():
    """Extract file-class relationships using AST parsing"""
    file_class_relationships = []
    
    # Get all Java files from the hierarchy data
    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']
    
    for _, file_row in java_files.iterrows():
        if 'file_path' in file_row and file_row['file_path']:
            file_path = file_row['file_path']
            file_name = file_row['destination_node']
            
            try:
                # Read and parse the Java file
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code = f.read().encode('utf-8')
                
                tree = parser.parse(source_code)
                root_node = tree.root_node
                
                # Find class declarations
                def find_classes_and_interfaces(node):
                    entities = []
                    if node.type == 'class_declaration':
                        # Find class name
                        for child in node.children:
                            if child.type == 'identifier':
                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(class_name), 'Class'))
                                break
                    elif node.type == 'interface_declaration':
                                # Find interface name
                                for child in node.children:
                                    if child.type == 'identifier':
                                        interface_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                        entities.append((to_pascal_case(interface_name), 'Interface'))
                                        break
                    elif node.type == 'enum_declaration':
                                # Find enum name
                                for child in node.children:
                                    if child.type == 'identifier':
                                        enum_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                        entities.append((to_pascal_case(enum_name), 'Enum'))
                                        break
                    # Recursively search child nodes
                    for child in node.children:
                        entities.extend(find_classes_and_interfaces(child))
                    
                    return entities
                
                classes_in_file = find_classes_and_interfaces(root_node)
                
                # Create file-class relationships
                for class_name in classes_in_file:
                    file_class_relationships.append({
                        'source_node': file_name,
                        'source_type': 'File',
                        'destination_node': class_name,
                        'destination_type': 'Class',
                        'relationship': 'DECLARES',
                        'parent': file_name,  # Track parent file
                        'file_path': file_path
                    })
                    
            except Exception as e:
                print(f" Error processing {file_path}: {e}")
                continue
    
    return file_class_relationships

# Execute Stage 2B: Extract file-class relationships
file_class_relationships = extract_file_class_relationships_ast()
df_file_class = pd.DataFrame(file_class_relationships)

# Append file-class relationships to the hierarchy DataFrame
if len(df_file_class) > 0:
    df_hierarchy = pd.concat([df_hierarchy, df_file_class], ignore_index=True)
    print(f" Added {len(df_file_class)} file-class relationships to Stage 2 data")
    
    # Update memory with file-class relationships
    memory['stage_2_results']['file_class_relationships'] = len(df_file_class)
    
    # Add validated edges for file-class relationships
    for _, row in df_file_class.iterrows():
        edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
        memory['validated_edges'].add(edge_key)
    
    save_memory(memory)
else:
    print(" No file-class relationships found")

print(f" Stage 2B Complete: Total relationships now {len(df_hierarchy)} (including {len(df_file_class)} file-class)")

# ========== STAGE 2C: ENHANCED FILE-CLASS RELATIONSHIP MATCHING ==========

def enhance_file_class_relationships():
    """Enhance file-class relationships with improved name matching and interface support"""
    print("🔧 Enhancing file-class relationships with improved matching...")
    
    additional_relationships = []
    
    # Get all files that don't have class relationships yet
    existing_file_class = set()
    for _, row in df_hierarchy.iterrows():
        if row['source_type'] == 'File' and row['relationship'] == 'DECLARES':
            existing_file_class.add(row['source_node'])
    
    # Get all Java files
    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']
    
    print(f"📋 Found {len(java_files)} Java files, {len(existing_file_class)} already have class relationships")
    
    for _, file_row in java_files.iterrows():
        file_name = file_row['destination_node']
        file_path = file_row.get('file_path', '')
        
        # Skip if already has relationships or not a Java file
        if file_name in existing_file_class or not file_path.endswith('.java'):
            continue
        
        # Extract base name from file
        base_name = file_name.replace('.java', '') if file_name.endswith('.java') else file_name
        base_name = to_pascal_case(base_name)
        
        # Try to match with class registry using case-insensitive comparison
        matched_classes = []
        for class_name in class_registry.keys():
            # Case-insensitive matching
            if class_name.lower() == base_name.lower():
                matched_classes.append(class_name)
        
        # If exact match found, create relationship
        for matched_class in matched_classes:
            try:
                # Read source code to determine if it's interface or class
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Determine entity type - check for interface keyword
                entity_type = 'Interface' if 'interface ' + matched_class in source_code or 'public interface ' + matched_class in source_code else 'Class'
                
                additional_relationships.append({
                    'source_node': file_name,
                    'source_type': 'File',
                    'destination_node': matched_class,
                    'destination_type': entity_type,
                    'relationship': 'DECLARES',
                    'file_path': file_path
                })
                
                print(f"  ✅ Enhanced match: {file_name} -> {matched_class} ({entity_type})")
                
            except Exception as e:
                print(f"  ⚠️ Error reading {file_path}: {e}")
                continue
    
    return additional_relationships

print("\n🔧 Stage 2C: Enhanced File-Class Relationship Matching function defined.")
print("📝 To execute: enhanced_relationships = enhance_file_class_relationships()")
print("📝 This will find missing file-class relationships using case-insensitive matching with the class registry.")

# ========== STAGE 3: AST EXTRACTION & CLASS REGISTRY ==========
# Combined from v9 and v10 - includes AST parsing and enhanced class registry

def read_source_code(file_path):
    """Read source code from file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_ast_relationships():
    """Extract AST relationships from Java files using tree-sitter"""
    ast_records = []
    
    for java_file in BASE_PATH.rglob("*.java"):
        try:
            source_code = read_source_code(java_file)
            tree = parser.parse(source_code)
            root_node = tree.root_node
            
            file_name = to_pascal_case(java_file.stem)
            
            def traverse_node(node, parent_class=None, parent_method=None):
                """Traverse AST nodes and extract relationships"""
                node_text = source_code[node.start_byte:node.end_byte].decode('utf-8')
                
                # Class declarations
                if node.type in ['class_declaration', 'interface_declaration', 'enum_declaration']:
                    for child in node.children:
                        if child.type == 'identifier':
                            class_name = to_pascal_case(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                            
                            # File-Class relationship (if not already added in Stage 2)
                            ast_records.append({
                                'source_node': file_name,
                                'source_type': 'File',
                                'destination_node': class_name,
                                'destination_type': 'Class',
                                'relationship': 'DECLARES',
                                'parent': file_name
                            })
                            
                            # Continue traversing with this class as parent
                            for grandchild in node.children:
                                traverse_node(grandchild, class_name, None)
                            return
                
                # Method declarations
                elif node.type == 'method_declaration':
                    for child in node.children:
                        if child.type == 'identifier':
                            method_name = to_pascal_case(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                            
                            if parent_class:
                                # Class-Method relationship
                                ast_records.append({
                                    'source_node': parent_class,
                                    'source_type': 'Class',
                                    'destination_node': method_name,
                                    'destination_type': 'Method',
                                    'relationship': 'CONTAINS',
                                    'parent': parent_class
                                })
                            
                            # Continue traversing with this method as parent
                            for grandchild in node.children:
                                traverse_node(grandchild, parent_class, method_name)
                            return
                
                # Variable declarations
                elif node.type in ['variable_declarator', 'field_declaration']:
                    for child in node.children:
                        if child.type == 'variable_declarator':
                            for grandchild in child.children:
                                if grandchild.type == 'identifier':
                                    var_name = source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8')
                                    
                                    # Skip temp variables
                                    if not is_temp_variable(var_name):
                                        context = parent_method if parent_method else parent_class
                                        context_type = 'Method' if parent_method else 'Class'
                                        
                                        if context:
                                            ast_records.append({
                                                'source_node': context,
                                                'source_type': context_type,
                                                'destination_node': var_name,
                                                'destination_type': 'Variable',
                                                'relationship': 'DECLARES',
                                                'parent': context
                                            })
                
                # Continue traversing children
                for child in node.children:
                    traverse_node(child, parent_class, parent_method)
            
            traverse_node(root_node)
            
        except Exception as e:
            print(f'⚠️ Error processing {java_file}: {e}')
            continue
    
    return ast_records

def create_ast_name_mapping(df_ast):
    """Create mapping from lowercase names to correct AST names"""
    name_mapping = {}
    for _, row in df_ast.iterrows():
        # Map source nodes
        if row['source_type'] in ['Class', 'Method']:
            name_mapping[row['source_node'].lower()] = row['source_node']
        # Map destination nodes
        if row['destination_type'] in ['Class', 'Method']:
            name_mapping[row['destination_node'].lower()] = row['destination_node']
    return name_mapping

# Execute Stage 3A: AST Extraction
ast_records = extract_ast_relationships()
df_ast = pd.DataFrame(ast_records)
print(f'✅ Stage 3A Complete: {len(df_ast)} AST relationships extracted')

# Create AST name mapping for correcting LLM output
ast_name_mapping = create_ast_name_mapping(df_ast)
print(f'📝 Created AST name mapping with {len(ast_name_mapping)} entries')

# ========== STAGE 3B: ENHANCED CLASS REGISTRY ==========
# Enhanced class registry from v10 with endpoint and database entity detection

# Patterns for analysis
PACKAGE_PATTERN = r'package\s+([\w\.]+);'
IMPORT_PATTERN = r'import\s+([\w\.]+);'
MAPPING_PATTERNS = {
    'GetMapping': r'@GetMapping\s*\(\s*["\']([^"\']+)["\']',
    'PostMapping': r'@PostMapping\s*\(\s*["\']([^"\']+)["\']',
    'PutMapping': r'@PutMapping\s*\(\s*["\']([^"\']+)["\']',
    'DeleteMapping': r'@DeleteMapping\s*\(\s*["\']([^"\']+)["\']',
    'RequestMapping': r'@RequestMapping\s*\(\s*["\']([^"\']+)["\']'
}

def extract_package_and_imports(source_code_str):
    """Extract package name and import list from Java source"""
    package_match = re.search(PACKAGE_PATTERN, source_code_str)
    package_name = package_match.group(1) if package_match else None
    import_matches = re.findall(IMPORT_PATTERN, source_code_str)
    return package_name, import_matches

def extract_api_endpoints(source_code_str):
    """Extract API endpoints using Spring annotations"""
    endpoints = []
    cleaned_code = remove_java_comments(source_code_str)
    
    for mapping_type, pattern in MAPPING_PATTERNS.items():
        matches = re.findall(pattern, cleaned_code, re.MULTILINE)
        for match in matches:
            path = match.strip()
            if path and (path.startswith('/') or path.startswith('${')):
                if not any(keyword in path.lower() for keyword in ['import', 'package', 'interface', 'class']):
                    method = mapping_type.replace('Mapping', '').upper() if mapping_type != 'RequestMapping' else 'GET'
                    endpoints.append({
                        'type': mapping_type,
                        'path': path,
                        'method': method
                    })
    return endpoints

def extract_database_entities(source_code_str):
    """Extract @Entity, @Table, and @Query usage from Java file"""
    entities = []

    # @Entity/@Table extraction
    if "@Entity" in source_code_str:
        table_matches = re.findall(r'@Table\s*\(\s*name\s*=\s*["\']([^"\']+)["\']', source_code_str)
        for table_name in table_matches:
            entities.append({'type': 'table', 'name': table_name.strip()})

        if not table_matches:
            class_match = re.search(r'(public\s+)?(class|abstract class|interface)\s+(\w+)', source_code_str)
            if class_match:
                class_name = class_match.group(3)
                snake_case = re.sub('([a-z0-9])([A-Z])', r'\1_\2', class_name).lower()
                entities.append({'type': 'table', 'name': snake_case})

    # @Query: detect raw SQL or JPQL references to tables
    query_pattern = r'@Query\s*\([^)]*["\']([^"\']*(?:FROM|from)\s+([\w]+)[^"\']*)["\']'
    query_matches = re.findall(query_pattern, source_code_str, re.MULTILINE | re.IGNORECASE)
    for _, table in query_matches:
        table = table.strip()
        if table and table.lower() not in {'select', 'where', 'group', 'order'}:
            entities.append({'type': 'table', 'name': table})

    return entities

def build_enhanced_class_registry():
    """Build enhanced class registry with improved memory integration"""
    enhanced_registry = {}
    
    for java_file in BASE_PATH.rglob("*.java"):
        try:
            with open(java_file, 'r', encoding='utf-8') as f:
                source_code_str = f.read()
            
            package_name, imports = extract_package_and_imports(source_code_str)
            endpoints = extract_api_endpoints(source_code_str)
            db_entities = extract_database_entities(source_code_str)
            
            # Apply improved name cleaning
            class_name = extract_clean_name(java_file.stem, 'class')
            fqcn = f'{package_name}.{class_name}' if package_name else class_name
            
            enhanced_registry[class_name] = {
                'fqcn': fqcn,
                'package': package_name,
                'file_path': str(java_file),
                'imports': imports,
                'endpoints': endpoints,
                'db_entities': db_entities,
                'source_code': source_code_str
            }
            
            # Extract methods for memory storage
            methods = re.findall(r'(?:public|private|protected)\s+\w+\s+(\w+)\s*\(', source_code_str)
            clean_methods = [extract_clean_name(m, 'method') for m in methods]
            
            # Store method signatures in memory for cross-stage reference
            for method in clean_methods:
                memory['method_signatures'][method] = {
                    'class': class_name,
                    'file_path': str(java_file),
                    'stage': 'stage_3_registry'
                }
            
        except Exception as e:
            print(f"⚠️ Error processing {java_file}: {e}")
            continue
    
    return enhanced_registry

# Execute Stage 3B: Class Registry
class_registry = build_enhanced_class_registry()

# Store in memory
memory['class_registry'] = class_registry
save_memory(memory)

print(f'✅ Stage 3B Complete: Enhanced class registry built with {len(class_registry)} classes')
print(f'📊 Memory updated with {len(memory["method_signatures"])} method signatures')

# ========== STAGE 3B: AST EXTRACTION ==========

def read_source_code(file_path):
    """Read source code from file"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read().encode('utf-8')

def extract_ast_structure(file_path):
    """Extract AST structure from Java file using tree-sitter"""
    records = []
    source_code = read_source_code(file_path)
    tree = parser.parse(source_code)
    root_node = tree.root_node
    file_name = os.path.basename(file_path)

    def clean_node_name(name):
        """Clean node names to remove prefixes and suffixes"""
        if not name:
            return name
        
        # Remove common prefixes
        prefixes_to_remove = ['method:', 'class:', 'variable:', 'field:']
        for prefix in prefixes_to_remove:
            if name.lower().startswith(prefix):
                name = name[len(prefix):]
        
        # Remove file extensions
        name = re.sub(r'\.(java|class)$', '', name, flags=re.IGNORECASE)
        
        return name.strip()

    def traverse(node, parent_type=None, parent_name=None):
        # Handle class declarations
        if node.type == 'class_declaration':
            class_name = None
            for child in node.children:
                if child.type == 'identifier':
                    class_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    # File -> Class relationship
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': class_name,
                        'destination_type': 'class',
                        'relationship': 'declares',
                        'file_path': file_path
                    })
                    
                    # Add API endpoints from registry
                    class_info = class_registry.get(class_name, {})
                    endpoints = class_info.get('endpoints', [])
                    for ep in endpoints:
                        endpoint_name = f"{ep['method']} {ep['path']}"
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': endpoint_name,
                            'destination_type': 'endpoint',
                            'relationship': 'declares',
                            'file_path': file_path
                        })
                    
                    # Add database entities from registry
                    db_entities = class_info.get('db_entities', [])
                    for entity in db_entities:
                        records.append({
                            'source_node': class_name,
                            'source_type': 'class',
                            'destination_node': entity['name'],
                            'destination_type': 'table',
                            'relationship': 'maps_to',
                            'file_path': file_path
                        })
                    break
            
            # Traverse children with class context
            for child in node.children:
                traverse(child, 'class', class_name)
                
        # Handle interface declarations
        elif node.type == 'interface_declaration':
            interface_name = None
            for child in node.children:
                if child.type == 'identifier':
                    interface_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    records.append({
                        'source_node': file_name,
                        'source_type': 'file',
                        'destination_node': interface_name,
                        'destination_type': 'interface',
                        'relationship': 'declares',
                        'file_path': file_path
                    })
                    break
            
            # Traverse children with interface context
            for child in node.children:
                traverse(child, 'interface', interface_name)
                
        # Handle method declarations - FIXED HIERARCHY
        elif node.type == 'method_declaration':
            method_name = None
            for child in node.children:
                if child.type == 'identifier':
                    method_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    
                    # CORRECT: Class -> Method (not Method -> Class)
                    if parent_name and parent_type in ['class', 'interface']:
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': method_name,
                            'destination_type': 'method',
                            'relationship': 'declares',
                            'file_path': file_path
                        })
                    break
            
            # Traverse children with method context
            for child in node.children:
                traverse(child, 'method', method_name)
                
        # Handle field declarations - FIXED HIERARCHY
        elif node.type == 'field_declaration':
            for child in node.children:
                if child.type == 'variable_declarator':
                    for grandchild in child.children:
                        if grandchild.type == 'identifier':
                            field_name = clean_node_name(source_code[grandchild.start_byte:grandchild.end_byte].decode('utf-8'))
                            
                            # CORRECT: Class -> Variable (not Variable -> Class)
                            if parent_name and parent_type == 'class':
                                records.append({
                                    'source_node': parent_name,
                                    'source_type': parent_type,
                                    'destination_node': field_name,
                                    'destination_type': 'variable',
                                    'relationship': 'has_field',
                                    'file_path': file_path
                                })
                                
        # Handle variable usage in methods - FIXED HIERARCHY
        elif node.type in ['assignment_expression', 'variable_declarator'] and parent_type == 'method':
            for child in node.children:
                if child.type == 'identifier':
                    var_name = clean_node_name(source_code[child.start_byte:child.end_byte].decode('utf-8'))
                    if var_name and var_name != 'this' and parent_name:
                        # CORRECT: Method -> Variable (not Variable -> Method)
                        records.append({
                            'source_node': parent_name,
                            'source_type': parent_type,
                            'destination_node': var_name,
                            'destination_type': 'variable',
                            'relationship': 'uses',
                            'file_path': file_path
                        })
        else:
            # Continue traversing for other node types
            for child in node.children:
                traverse(child, parent_type, parent_name)

    traverse(root_node)
    return records

# Execute AST extraction
ast_records = []
for root, _, files in os.walk(BASE_PATH):
    for file in files:
        if file.endswith('.java'):
            file_path = os.path.join(root, file)
            try:
                ast_records.extend(extract_ast_structure(file_path))
            except Exception as e:
                print(f'⚠️ Error processing {file}: {e}')
                continue

df_ast = pd.DataFrame(ast_records)
print(f' Stage 3B Complete: {len(df_ast)} AST relationships extracted')

# Create AST name mapping for correcting LLM output
def create_ast_name_mapping(df_ast):
    """Create mapping from lowercase names to correct AST names"""
    name_mapping = {}
    
    # Extract all unique class and method names from AST
    for _, row in df_ast.iterrows():
        source_name = row['source_node']
        dest_name = row['destination_node']
        source_type = row['source_type']
        dest_type = row['destination_type']
        
        # Map class names
        if source_type == 'class' and source_name:
            name_mapping[source_name.lower()] = source_name
        if dest_type == 'class' and dest_name:
            name_mapping[dest_name.lower()] = dest_name
            
        # Map method names
        if source_type == 'method' and source_name:
            name_mapping[source_name.lower()] = source_name
        if dest_type == 'method' and dest_name:
            name_mapping[dest_name.lower()] = dest_name
    
    return name_mapping

# Create the mapping
ast_name_mapping = create_ast_name_mapping(df_ast)
print(f" Created AST name mapping with {len(ast_name_mapping)} entries")

# Store AST results in memory
memory['ast_relationships'] = len(df_ast)
memory['ast_name_mapping'] = ast_name_mapping
save_memory(memory)

# ========== ENHANCED AST NAME MAPPING ==========

def create_enhanced_ast_name_mapping(ast_df, class_registry):
    """Create enhanced AST name mapping with case-insensitive matching"""
    print("🔧 Creating enhanced AST name mapping with case-insensitive matching...")
    
    enhanced_mapping = {}
    
    # Get all unique names from AST
    ast_names = set()
    for col in ['source_node', 'destination_node']:
        if col in ast_df.columns:
            ast_names.update(ast_df[col].dropna().unique())
    
    # Get all class registry names
    registry_names = set(class_registry.keys())
    
    print(f"📊 AST names: {len(ast_names)}, Registry names: {len(registry_names)}")
    
    # Create case-insensitive mapping
    for ast_name in ast_names:
        if not ast_name:
            continue
            
        # First try exact match
        if ast_name in registry_names:
            enhanced_mapping[ast_name.lower()] = ast_name
            continue
        
        # Try case-insensitive match
        for registry_name in registry_names:
            if ast_name.lower() == registry_name.lower():
                enhanced_mapping[ast_name.lower()] = registry_name
                print(f"  🔗 Case-insensitive match: {ast_name} -> {registry_name}")
                break
        
        # FIXED: Try partial matching for method names (e.g., "getUserId" -> "GetUserId")
        if ast_name.lower() not in enhanced_mapping:
            for registry_name in registry_names:
                if (ast_name.lower().replace('_', '').replace('-', '') == 
                    registry_name.lower().replace('_', '').replace('-', '')):
                    enhanced_mapping[ast_name.lower()] = registry_name
                    print(f"  🔗 Partial match: {ast_name} -> {registry_name}")
                    break
    
    print(f"✅ Enhanced mapping created: {len(enhanced_mapping)} mappings")
    return enhanced_mapping

def apply_enhanced_name_correction(df, enhanced_mapping):
    """Apply enhanced name correction to DataFrame"""
    print("🔧 Applying enhanced name correction...")
    
    corrections_made = 0
    
    for col in ['source_node', 'destination_node']:
        if col in df.columns:
            for idx, value in df[col].items():
                if pd.notna(value) and value.lower() in enhanced_mapping:
                    corrected_value = enhanced_mapping[value.lower()]
                    if corrected_value != value:
                        df.at[idx, col] = corrected_value
                        corrections_made += 1
    
    print(f"✅ Applied {corrections_made} name corrections")
    return df

enhanced_mapping = create_enhanced_ast_name_mapping(ast_df, class_registry)
df_corrected = apply_enhanced_name_correction(df, enhanced_mapping)

# ========== STAGE 4B: CONSOLIDATED LLM PROCESSING ==========
# User preference: Consolidate file-class relationship detection into LLM stage
# This stage handles: File-Class-Method-Variables relationships via LLM

def escape_braces_for_langchain(text):
    """Escape curly braces in Java code to prevent LangChain template variable conflicts"""
    if not text:
        return text
    return text.replace('{', '{{').replace('}', '}}')

def build_consolidated_stage4b_prompt(file_path, ast_df, class_registry):
    """Build consolidated Stage 4B prompt for file-class-method-variable relationships"""
    
    # Extract file name from path
    file_name = to_pascal_case(Path(file_path).stem) if file_path else 'UnknownFile'
    
    # Get class registry info for this file
    class_info = class_registry.get(file_name, {})
    endpoints = class_info.get('endpoints', [])
    db_entities = class_info.get('db_entities', [])
    
    # Build endpoint context
    endpoint_context = ''
    if endpoints:
        endpoint_list = [f"- {ep['method']} {ep['path']}" for ep in endpoints]
        endpoint_context = f"\nAPI Endpoints in {file_name}:\n" + "\n".join(endpoint_list)
    
    # Build database context
    db_context = ''
    if db_entities:
        db_list = [f"- {entity['name']} ({entity['type']})" for entity in db_entities]
        db_context = f"\nDatabase Entities in {file_name}:\n" + "\n".join(db_list)
    
    # Build consolidated prompt (handles all relationship types)
    prompt = f"""
You are a Java code lineage extraction engine. Extract ALL structural and data flow relationships from this Java file.

CURRENT FILE: {file_name}
{endpoint_context}
{db_context}

EXTRACT ALL RELATIONSHIP TYPES:

1. STRUCTURAL RELATIONSHIPS:
   - File -[DECLARES]-> Class (detect all classes/interfaces in file)
   - Class -[CONTAINS]-> Method (all methods in classes)
   - Class -[HAS_FIELD]-> Variable (class-level fields)
   - Method -[DECLARES]-> Variable (method parameters and local variables)
   - Class -[DECLARES]-> Endpoint (for @RestController classes)
   - Class -[MAPS_TO]-> Table (for @Entity classes)

2. DATA FLOW RELATIONSHIPS:
   - Method -[PRODUCES]-> Variable (method creates/returns variable)
   - Variable -[FLOWS_TO]-> Variable (data passing between variables)
   - Variable -[TRANSFORMS_TO]-> Variable (data conversion/processing)
   - Method -[USES]-> Variable (method reads/uses variable)
   - Variable -[RETURNS_DATA_TO]-> Variable (return value assignments)

3. NAMING RULES:
   - Use PascalCase for Files, Classes, Methods (e.g., DateUtil, ConvertDtoToEntity)
   - Use original names for Variables (e.g., orderDto, entityList)
   - Remove prefixes like 'method:', 'class:', 'variable:'
   - For endpoints: use descriptive names like 'GetOrdersEndpoint'
   - For tables: use PascalCase table names

4. VARIABLE FILTERING:
   - EXCLUDE temp variables: i, j, k, l, m, n, x, y, z, temp, tmp, index, idx, counter, count
   - INCLUDE meaningful variables: DTOs, entities, service calls, business data

5. CONTEXT TRACKING:
   - Global variables (class fields): context = class name
   - Local variables (method variables): context = method name

Extract relationships in format:
[SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName

Return ONLY the relationship triples, no explanations.
"""
    return prompt.replace("{", "{{").replace("}", "}}")

def smart_chunk_strategy(file_path, content):
    """Smart chunking: whole file if <1000 lines, language chunks if larger"""
    lines = content.count('\n') + 1
    escaped_content = escape_braces_for_langchain(content)
    
    if lines <= 1000:
        return [{'content': escaped_content, 'metadata': {'source': file_path, 'chunk_type': 'whole_file'}}]
    else:
        splitter = RecursiveCharacterTextSplitter.from_language(
            language=LC_Language.JAVA,
            chunk_size=8000,
            chunk_overlap=400
        )
        doc = Document(page_content=escaped_content, metadata={'source': file_path})
        chunks = splitter.split_documents([doc])
        return [{'content': chunk.page_content, 'metadata': {**chunk.metadata, 'chunk_type': 'language_chunk'}} for chunk in chunks]

# Collect documents with smart chunking
smart_docs = []
for java_file in BASE_PATH.rglob("*.java"):
    try:
        with open(java_file, 'r', encoding='utf-8') as f:
            content = f.read()
        smart_docs.extend(smart_chunk_strategy(str(java_file), content))
    except Exception as e:
        print(f"⚠️ Error loading {java_file}: {e}")
        continue

print(f"✅ Stage 4B Setup: {len(smart_docs)} documents prepared for LLM processing")

# ========== STAGE 4B: CONSOLIDATED LLM EXECUTION ==========
# Handles File-Class-Method-Variables relationships via LLM

def determine_parent(source_node, source_type, dest_node, dest_type):
    """Determine parent entity for hierarchy tracking"""
    dest_type_lower = dest_type.lower()
    source_type_lower = source_type.lower()
    
    # Method parent is class
    if dest_type_lower == 'method' and source_type_lower == 'class':
        return source_node
    # Variable parent is class (for fields) or method (for local vars)
    elif dest_type_lower == 'variable' and source_type_lower in ['class', 'method']:
        return source_node
    # Class parent is file
    elif dest_type_lower == 'class' and source_type_lower == 'file':
        return source_node
    # File parent is folder
    elif dest_type_lower == 'file' and source_type_lower == 'folder':
        return source_node
    
    return None

def run_consolidated_stage4b():
    """Execute consolidated Stage 4B: LLM Processing for all relationships"""
    print("🚀 Starting Stage 4B: Consolidated LLM Processing...")
    start_time = time.time()
    
    all_llm_lineage = []
    
    for doc_info in tqdm(smart_docs, desc='🤖 Stage 4B: Consolidated LLM Processing'):
        file_path = doc_info['metadata'].get('source')
        
        # Use consolidated prompt that handles all relationship types
        enhanced_prompt = build_consolidated_stage4b_prompt(file_path, df_ast, class_registry)
        
        # Configure transformer with all allowed relationships
        transformer = LLMGraphTransformer(
            llm=llm,
            additional_instructions=enhanced_prompt,
            allowed_nodes=['File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint'],
            allowed_relationships=[
                # Structural relationships
                ('File', 'DECLARES', 'Class'),
                ('File', 'DECLARES', 'Interface'),
                ('Class', 'CONTAINS', 'Method'),
                ('Interface', 'CONTAINS', 'Method'),
                ('Class', 'HAS_FIELD', 'Variable'),
                ('Method', 'DECLARES', 'Variable'),
                ('Class', 'DECLARES', 'Endpoint'),
                ('Class', 'MAPS_TO', 'Table'),
                # Data flow relationships
                ('Method', 'PRODUCES', 'Variable'),
                ('Method', 'USES', 'Variable'),
                ('Variable', 'FLOWS_TO', 'Variable'),
                ('Variable', 'TRANSFORMS_TO', 'Variable'),
                ('Variable', 'RETURNS_DATA_TO', 'Variable')
            ],
            strict_mode=False
        )
        
        try:
            # Ensure content is properly escaped for LangChain
            escaped_content = escape_braces_for_langchain(doc_info['content'])
            doc = Document(page_content=escaped_content, metadata=doc_info['metadata'])
            graph_docs = transformer.convert_to_graph_documents([doc])
            
            for gd in graph_docs:
                for rel in gd.relationships:
                    s_node = rel.source.id.strip()
                    s_type = rel.source.type.strip().lower()
                    t_node = rel.target.id.strip()
                    t_type = rel.target.type.strip().lower()
                    rel_type = rel.type.strip().lower()
                    
                    # Validate and fix relationship directions
                    def validate_relationship_direction(source_type, rel_type, target_type):
                        """Validate relationship direction and fix if needed"""
                        valid_directions = {
                            ('class', 'declares', 'method'),
                            ('interface', 'declares', 'method'),
                            ('class', 'declares', 'endpoint'),
                            ('method', 'calls', 'method'),
                            ('class', 'has_field', 'variable'),
                            ('method', 'uses', 'variable'),
                            ('class', 'uses', 'class'),
                            ('interface', 'extends', 'interface'),
                            ('class', 'extends', 'class'),
                            ('class', 'implements', 'interface'),
                            ('class', 'maps_to', 'table'),
                            ('method', 'reads_from', 'table'),
                            ('method', 'writes_to', 'table')
                        }
                        
                        # Check if current direction is valid
                        if (source_type, rel_type, target_type) in valid_directions:
                            return source_type, rel_type, target_type, False
                        
                        # Check if reverse direction is valid
                        if (target_type, rel_type, source_type) in valid_directions:
                            return target_type, rel_type, source_type, True
                        
                        # Invalid relationship - skip
                        return None, None, None, False
                    
                    # Validate direction
                    validated_s_type, validated_rel_type, validated_t_type, was_reversed = validate_relationship_direction(s_type, rel_type, t_type)
                    
                    if validated_s_type is None:
                        print(f"⚠️ Skipping invalid relationship: {s_type} -[{rel_type}]-> {t_type}")
                        continue
                    
                    if was_reversed:
                        print(f"🔄 Fixed direction: {s_type}:{s_node} -[{rel_type}]-> {t_type}:{t_node} → {validated_s_type}:{t_node} -[{validated_rel_type}]-> {validated_t_type}:{s_node}")
                        s_node, t_node = t_node, s_node
                        s_type, t_type = validated_s_type, validated_t_type
                        rel_type = validated_rel_type

                    def normalize_entity_improved(entity_name, entity_type):
                        if not entity_name:
                            return entity_name
                        
                        # Apply improved name cleaning
                        clean_name = extract_clean_name(entity_name, entity_type)
                        
                        # CORRECTION: Use AST mapping to fix case inconsistencies
                        if entity_type in ['class', 'method'] and clean_name:
                            # Look up correct name from AST mapping
                            correct_name = ast_name_mapping.get(clean_name.lower())
                            if correct_name:
                                clean_name = correct_name
                                #print(f"🔧 Corrected {entity_type} name: {entity_name} → {clean_name}")
                        
                        # Task 4: Check if it's actually a method
                        if entity_type == 'variable' and is_method_name(entity_name, class_registry):
                            return None  # Skip method names incorrectly identified as variables
                        
                        # Special handling for variables
                        if entity_type == 'variable':
                            # Filter out temp variables
                            if is_temp_variable(clean_name):
                                return None
                            
                            # Task 3: Create unique variable name with context
                            # Extract context from file path
                            context_class = None
                            if file_path:
                                import os
                                file_name = os.path.basename(file_path).replace('.java', '')
                                context_class = to_pascal_case(file_name)
                                clean_name = create_unique_variable_name(clean_name, context_class, 'class')
                            
                            # Store variable context in memory
                            if '.' in entity_name:
                                context_part = entity_name.split('.')[0]
                                context = extract_clean_name(context_part, 'method')
                                memory['variable_contexts'][clean_name] = {
                                    'context': context,
                                    'context_type': 'method',
                                    'full_name': entity_name
                                }
                        
                        return clean_name

                    s_node = normalize_entity_improved(s_node, s_type)
                    t_node = normalize_entity_improved(t_node, t_type)

                    if not s_node or not t_node or s_node == t_node:
                        continue
                    
                    # Enforce correct relationship directions
                    valid_directions = {
                        ('file', 'declares', 'class'),
                        ('file', 'declares', 'interface'),
                        ('class', 'declares', 'method'),
                        ('interface', 'declares', 'method'),
                        ('class', 'declares', 'endpoint'),
                        ('class', 'has_field', 'variable'),
                        ('method', 'uses', 'variable'),
                        ('class', 'maps_to', 'table'),
                        ('class', 'extends', 'class'),
                        ('class', 'implements', 'interface'),
                        ('interface', 'extends', 'interface'),
                        ('method', 'calls', 'method'),
                        ('method', 'reads_from', 'table'),
                        ('method', 'writes_to', 'table')
                    }
                    
                    if (s_type, rel_type, t_type) not in valid_directions:
                        continue

                    all_llm_lineage.append({
                        'source_node': s_node,
                        'source_type': s_type.title(),
                        'destination_node': t_node,
                        'destination_type': t_type.title(),
                        'relationship': rel_type.upper(),
                        'parent': determine_parent(s_node, s_type, t_node, t_type),  # Track parent
                        'file_path': file_path
                    })
                    
        except Exception as e:
            print(f"⚠️ LLM processing error for {file_path}: {e}")
            continue

    df_llm_lineage = pd.DataFrame(all_llm_lineage)
    
    # Update memory with LLM results
    for _, row in df_llm_lineage.iterrows():
        edge_key = f"{row['source_node']}-{row['relationship']}-{row['destination_node']}"
        memory['validated_edges'].add(edge_key)
    
    save_memory(memory)
    
    end_time = time.time()
    print(f'✅ Stage 4B Complete: {len(df_llm_lineage)} relationships in {end_time-start_time:.1f}s')
    return df_llm_lineage

# ========== OLD STAGE 5 FUNCTION - DEPRECATED ==========
# This function is replaced by Stage 5B (Advanced Transformation Analysis)
# Keeping for reference but not used in pipeline execution
def run_stage_5_processing_DEPRECATED():
    """DEPRECATED: Execute Stage 5: Batch Transformation Analysis"""
    print("🤖 Starting Stage 5: Transformation Analysis (Parallel)")
    start_time = time.time()
    
    # Batch configuration for scalability
    BATCH_SIZE = 50  # Process 50 classes at a time
    
    # Initialize long-term memory for persistent storage
    long_term_memory = {
        'all_validated_edges': set(),
        'global_variable_contexts': {},
        'global_method_signatures': {},
        'processed_classes': set()
    }
    
    # Prepare class batches
    class_names = list(class_registry.keys())
    class_batches = [class_names[i:i+BATCH_SIZE] for i in range(0, len(class_names), BATCH_SIZE)]
    
    all_transformation_relationships = []
    
    # Process each batch with memory optimization
    for batch_num, batch_classes in enumerate(class_batches):
        # Initialize short-term memory for this batch only
        short_term_memory = {
            'variable_contexts': {},
            'method_signatures': {},
            'current_batch_edges': set()
        }
        
        batch_relationships = []
        
        # Process each class in the batch
        for class_name in tqdm(batch_classes, desc=f"Batch {batch_num + 1} Processing"):
            if class_name not in class_registry:
                continue
                
            class_info = class_registry[class_name]
            source_code = class_info['source_code']
            escaped_source_code = escape_braces_for_langchain(source_code)
            
            try:
                # Enhanced transformation prompt
                transformation_prompt = f"""
You are a Java data flow analysis engine for class: {class_name}

EXTRACT DATA FLOW RELATIONSHIPS:

1. VARIABLE TRANSFORMATIONS & FLOWS:
   - Variable -[FLOWS_TO]-> Variable (data passing)
   - Variable -[TRANSFORMS_TO]-> Variable (data conversion)
   - Method -[PRODUCES]-> Variable (method creates variable)
   - Variable-[:RETURNS_DATA_TO]-> Variable (For Method Return Valuse)
    
2. DATABASE OPERATIONS:
   - Method -[READS_FROM]-> Table (SELECT operations)
   - Method -[WRITES_TO]-> Table (INSERT/UPDATE/DELETE)
   - Variable -[PERSISTS_TO]-> Table (entity persistence)

3. API RELATIONSHIPS:
   - Class -[EXPOSES]-> Endpoint (REST endpoints)
   - Method -[MAPS_TO]-> Endpoint (method-endpoint mapping)
   - Endpoint -[ACCEPTS]-> Variable (request parameters)
   - Endpoint -[RETURNS]-> Variable (response data)

4. METHOD OPERATIONS:
   - Method -[CALLS]-> Method (method invocations)
   - Method -[INVOKES]-> ExternalService (external API calls)

IMPORTANT: Do NOT extract relationships for temporary/loop variables like: i, j, k, l, m, n, x, y, z, index, idx, iter, counter, count, temp, tmp, temporary, temp1, temp2
Focus only on meaningful business variables, class fields, method parameters, and return values.

CODE:
{escaped_source_code}

Return format: [SourceType]:SourceName -[RELATIONSHIP]-> [TargetType]:TargetName
"""
                
                response = llm.invoke(transformation_prompt)
                content = response.content if hasattr(response, 'content') else str(response)
                
                doc = Document(page_content=content, metadata={'class_name': class_name})
                
                transformer = LLMGraphTransformer(
                    llm=llm,
                    allowed_nodes=['variable', 'method', 'table', 'class', 'endpoint', 'database', 'externalservice'],
                    allowed_relationships=[
                        ('variable', 'flows_to', 'variable'),
                        ('variable', 'transforms_to', 'variable'),
                        ('method', 'produces', 'variable'),
                        ('method', 'reads_from', 'table'),
                        ('method', 'writes_to', 'table'),
                        ('variable', 'persists_to', 'table'),
                        ('class', 'exposes', 'endpoint'),
                        ('method', 'maps_to', 'endpoint'),
                        ('endpoint', 'accepts', 'variable'),
                        ('endpoint', 'returns', 'variable'),
                        ('method', 'calls', 'method'),
                        ('method', 'invokes', 'externalservice')
                    ],
                    strict_mode=False,
                    node_properties=False,
                    relationship_properties=False,
                )
                
                graph_docs = transformer.convert_to_graph_documents([doc])
                
                for gd in graph_docs:
                    for rel in gd.relationships:
                        s_type = rel.source.type.title()
                        t_type = rel.target.type.title()
                        s_node = extract_clean_name(rel.source.id, s_type.lower())
                        t_node = extract_clean_name(rel.target.id, t_type.lower())
                        rel_type = rel.type.upper()
                        
                        # Apply AST correction and improved variable handling
                        if s_type.lower() in ['class', 'method'] and s_node:
                            correct_name = ast_name_mapping.get(s_node.lower())
                            if correct_name:
                                s_node = correct_name
                        
                        if t_type.lower() in ['class', 'method'] and t_node:
                            correct_name = ast_name_mapping.get(t_node.lower())
                            if correct_name:
                                t_node = correct_name
                        
                        # Task 3: Variable uniqueness (LLM handles temp filtering)
                        if s_type == 'Variable':
                            if is_temp_variable(s_node):
                                continue
                            # Make variable unique with context
                            s_node = create_unique_variable_name(s_node, class_name, 'class')
                        
                        if t_type == 'Variable':
                            if is_temp_variable(t_node):
                                continue
                            # Make variable unique with context
                            t_node = create_unique_variable_name(t_node, class_name, 'class')
                        
                        # Task 4: Skip if method incorrectly identified as variable
                        if s_type == 'Variable' and is_method_name(s_node.split('.')[-1] if '.' in s_node else s_node, class_registry):
                            continue
                        if t_type == 'Variable' and is_method_name(t_node.split('.')[-1] if '.' in t_node else t_node, class_registry):
                            continue
                        
                        if not s_node or not t_node or s_node == t_node:
                            continue
                        
                        relationship = {
                            'source_node': s_node,
                            'source_type': s_type,
                            'destination_node': t_node,
                            'destination_type': t_type,
                            'relationship': rel_type,
                            'class_context': class_name,
                            'stage': 'batch_transformations'
                        }
                        
                        batch_relationships.append(relationship)
                        
                        # Store in short-term memory for this batch
                        edge_key = f"{s_node}-{rel_type}-{t_node}"
                        short_term_memory['current_batch_edges'].add(edge_key)
                
            except Exception as llm_error:
                print(f"LLM transformation error for {class_name}: {llm_error}")
                continue
        
        # Move batch results to long-term memory
        long_term_memory['all_validated_edges'].update(short_term_memory['current_batch_edges'])
        long_term_memory['processed_classes'].update(batch_classes)
        
        # Add batch relationships to overall results
        all_transformation_relationships.extend(batch_relationships)
        
        # Memory cleanup
        del short_term_memory
        del batch_relationships
        import gc
        gc.collect()
    
    end_time = time.time()
    print(f'✅ Stage 5 Complete: {len(all_transformation_relationships)} relationships in {end_time-start_time:.1f}s')
    return pd.DataFrame(all_transformation_relationships)

# Execute Stage 4B: LLM Processing
print("🚀 Starting Stage 4B: LLM Processing...")
df_llm_lineage = run_stage_4b_processing()
print(f"📊 Stage 4B Complete: {len(df_llm_lineage)} relationships")

# Note: Stage 5 is replaced by Stage 5B (Advanced Transformation Analysis)
print("\n📝 Stage 5 is replaced by Stage 5B for enhanced variable transformations")

# Task 5: Remove duplicates after AST name mapping
print("\n🔧 Task 5: Removing duplicates after AST name mapping...")

# FIXED: Enhanced duplicate removal function
def remove_duplicates_enhanced(df, stage_name):
    """Remove duplicates with enhanced logic - FIXED"""
    print(f"🔧 Removing duplicates from {stage_name} with enhanced logic...")
    
    initial_count = len(df)
    
    # First pass: remove exact duplicates
    df = df.drop_duplicates(
        subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'],
        keep='first'
    )
    
    after_exact = len(df)
    
    # Second pass: remove semantic duplicates (same relationship, different context)
    # Keep the one with more specific context (e.g., with file_path)
    if 'file_path' in df.columns:
        df = df.sort_values(['file_path'], na_position='first')
    
    df = df.drop_duplicates(
        subset=['source_node', 'destination_node', 'relationship'],
        keep='first'
    ).reset_index(drop=True)
    
    final_count = len(df)
    
    print(f"  📊 {stage_name}: {initial_count} -> {after_exact} -> {final_count}")
    print(f"  📊 Removed: {initial_count - final_count} duplicates")
    
    return df

# Remove duplicates from Stage 4B results
df_llm_lineage = remove_duplicates_enhanced(df_llm_lineage, 'Stage 4B')

print(f"✅ Enhanced duplicate removal complete")
print(f"📊 Final Stage 4B: {len(df_llm_lineage)} unique relationships")

# ========== STAGE 5B: ADVANCED TRANSFORMATION ANALYSIS ==========

def run_stage_5b_advanced_transformations():
    """Execute Stage 5B: Advanced Data Transformation Analysis for Enterprise Scale"""
    print("🏭 Starting Stage 5B: Advanced Transformation Analysis (Enterprise Scale)")
    start_time = time.time()
    
    # Load memory and class registry
    memory = load_memory()
    class_registry = memory.get('class_registry', {})
    variable_contexts = memory.get('variable_contexts', {})
    variable_flows = memory.get('variable_flows', {})
    
    if not class_registry:
        print("❌ No class registry found. Run previous stages first.")
        return pd.DataFrame()
    
    print(f"📊 Processing {len(class_registry)} classes for advanced transformations")
    
    # Enhanced LLM prompt for variable transformations
    enhanced_prompt = """
You are an advanced Java data flow analysis engine specializing in variable transformations and method-level data flows.

CRITICAL TRANSFORMATION PATTERNS TO CAPTURE:

1. METHOD VARIABLE PRODUCTION:
   - When a variable is assigned from a method call: Method -[PRODUCES]-> Variable
   - Example: String result = service.processData(input) → processData -[PRODUCES]-> result

2. COLLECTION OPERATIONS:
   - When collections are updated: Variable -[FLOWS_TO]-> CollectionEntry
   - map.put(key, value) → value -[FLOWS_TO]-> CollectionEntry:map.key
   - list.add(item) → item -[FLOWS_TO]-> CollectionEntry:list

3. VARIABLE TRANSFORMATIONS INSIDE METHODS:
   - Data type conversions: Variable -[TRANSFORMS_TO]-> Variable
   - String operations: originalStr -[TRANSFORMS_TO]-> processedStr
   - Object mapping: dto -[TRANSFORMS_TO]-> entity

4. COMPLEX FLOWS:
   - Method chaining: var1 -[FLOWS_TO]-> method1 -[PRODUCES]-> var2 -[FLOWS_TO]-> method2
   - Conditional assignments: condition ? var1 : var2 -[FLOWS_TO]-> result
   - Loop variable updates: loopVar -[TRANSFORMS_TO]-> updatedLoopVar

5. RETURN VALUE FLOWS:
   - Method return values: Variable -[RETURNS_DATA_TO]-> Variable
   - Return transformations: processedData -[RETURNS_DATA_TO]-> methodResult

FOCUS ON CAPTURING TRANSFORMATIONS THAT OCCUR INSIDE METHOD BODIES, NOT JUST METHOD SIGNATURES.

Output format: source_node|relationship|destination_node|source_type|destination_type
"""
    
    all_relationships = []
    
    # Process classes in batches for memory efficiency
    class_names = list(class_registry.keys())
    batch_size = 5  # Smaller batches for detailed analysis
    
    for i in range(0, len(class_names), batch_size):
        batch_classes = class_names[i:i+batch_size]
        print(f"🔄 Processing batch {i//batch_size + 1}/{(len(class_names) + batch_size - 1)//batch_size}")
        
        for class_name in batch_classes:
            class_info = class_registry[class_name]
            
            # Build enhanced context using variable registry
            context_info = f"""
Class: {class_name}
File: {class_info.get('file_path', 'Unknown')}

Variable Context from Registry:
"""
            
            # Add variable context information
            for var_name, var_info in variable_contexts.items():
                if var_info.get('context') == class_name or class_name in var_name:
                    context_info += f"- {var_name}: {var_info.get('context_type', 'unknown')} scope in {var_info.get('context', 'unknown')}\n"
            
            # Add method signatures for better understanding
            method_sigs = memory.get('method_signatures', {})
            if class_name in method_sigs:
                context_info += f"\nMethod Signatures:\n"
                for method, sig in method_sigs[class_name].items():
                    context_info += f"- {method}: {sig}\n"
            
            # Add source code for analysis
            source_code = class_info.get('source_code', '')
            if source_code:
                context_info += f"\nSource Code:\n{source_code[:2000]}..."  # Limit for token efficiency
            
            try:
                # Process with LLM
                response = client.chat.completions.create(
                    model="gpt-4o",
                    messages=[
                        {"role": "system", "content": enhanced_prompt},
                        {"role": "user", "content": f"Analyze this Java class for variable transformations:\n\n{context_info}"}
                    ],
                    temperature=0.1,
                    max_tokens=2000
                )
                
                # Parse response
                content = response.choices[0].message.content
                relationships = parse_llm_response_enhanced(content, class_name)
                all_relationships.extend(relationships)
                
                # Update variable flows in memory
                for rel in relationships:
                    if rel['relationship'] in ['FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES']:
                        flow_key = f"{rel['source_node']}->{rel['destination_node']}"
                        variable_flows[flow_key] = {
                            'relationship': rel['relationship'],
                            'class_context': class_name,
                            'timestamp': time.time()
                        }
                
                time.sleep(1)  # Rate limiting
                
            except Exception as e:
                print(f"❌ Error processing {class_name}: {e}")
                continue
    
    # Update memory with new variable flows
    memory['variable_flows'] = variable_flows
    save_memory(memory)
    
    # Convert to DataFrame
    if all_relationships:
        df_advanced = pd.DataFrame(all_relationships)
        print(f"✅ Stage 5B Complete: {len(df_advanced)} advanced transformation relationships extracted")
        print(f"⏱️ Processing time: {time.time() - start_time:.1f}s")
        return df_advanced
    else:
        print("⚠️ No advanced transformations found")
        return pd.DataFrame()

# Helper function for parsing enhanced LLM responses
def parse_llm_response_enhanced(content, class_context):
    """Parse LLM response with enhanced variable transformation patterns"""
    relationships = []
    
    if not content:
        return relationships
    
    lines = content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('#') or line.startswith('//'):
            continue
            
        # Look for pipe-separated format: source|relationship|destination|source_type|dest_type
        if '|' in line:
            parts = [p.strip() for p in line.split('|')]
            if len(parts) >= 3:
                source_node = parts[0]
                relationship = parts[1].upper().replace('-', '_').replace('[', '').replace(']', '')
                dest_node = parts[2]
                
                # Determine types based on content and context
                source_type = determine_node_type_enhanced(source_node, class_context)
                dest_type = determine_node_type_enhanced(dest_node, class_context)
                
                # Validate relationship types for transformations
                valid_transform_rels = {
                    'FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES', 'RETURNS_DATA_TO',
                    'PERSISTS_TO', 'READS_FROM', 'WRITES_TO'
                }
                
                if relationship in valid_transform_rels:
                    relationships.append({
                        'source_node': to_pascal_case(source_node),
                        'relationship': relationship,
                        'destination_node': to_pascal_case(dest_node),
                        'source_type': source_type,
                        'destination_type': dest_type,
                        'parent': determine_parent(source_node, source_type, dest_node, dest_type),
                        'class_context': class_context
                    })
    
    return relationships

def determine_node_type_enhanced(node_name, class_context):
    """Enhanced node type determination for variable transformations"""
    node_lower = node_name.lower()
    
    # Collection entries (special case for map.put, list.add patterns)
    if 'collectionentry:' in node_lower or '.key' in node_lower or '.entry' in node_lower:
        return 'Variable'
    
    # Method patterns
    if any(pattern in node_lower for pattern in ['()', 'method', 'process', 'get', 'set', 'create', 'update', 'delete', 'find']):
        return 'Method'
    
    # Database/Table patterns
    if any(pattern in node_lower for pattern in ['table', 'entity', 'repository', 'dao']):
        return 'Table'
    
    # Endpoint patterns
    if any(pattern in node_lower for pattern in ['endpoint', 'api', 'rest', 'controller']):
        return 'Endpoint'
    
    # Default to Variable for transformation analysis
    return 'Variable'

print("\n🏭 Stage 5B: Advanced Transformation Analysis function defined.")
print("📝 To execute: df_advanced_transformations = run_stage_5b_advanced_transformations()")
# Execute Stage 5B for advanced transformation analysis
df_advanced_transformations = run_stage_5b_advanced_transformations()

# ========== STAGE 6: FINAL CONSOLIDATION ==========

# Combine all DataFrames
all_dataframes = []

if len(df_hierarchy) > 0:
    all_dataframes.append(df_hierarchy)
    print(f"Hierarchy relationships: {len(df_hierarchy)}")
    

if len(df_llm_lineage) > 0:
    all_dataframes.append(df_llm_lineage)
    print(f"LLM lineage relationships: {len(df_llm_lineage)}")

# Note: Using Stage 5B (Advanced Transformations) instead of Stage 5
if len(df_advanced_transformations) > 0:
    all_dataframes.append(df_advanced_transformations)
    print(f"Advanced transformation relationships (Stage 5B): {len(df_advanced_transformations)}")

# Consolidate all relationships
if all_dataframes:
    df_final = pd.concat(all_dataframes, ignore_index=True)
else:
    df_final = pd.DataFrame()

# FIXED: Enhanced duplicate removal for final consolidation
print("🔧 Applying enhanced duplicate removal to final dataset...")
df_final = remove_duplicates_enhanced(df_final, 'Final Consolidation')

# Filter to only allowed nodes and relationships
allowed_nodes = {'Folder', 'File', 'Class', 'Interface', 'Method', 'Variable', 'Table', 'Endpoint', 'Database', 'Externalservice'}
allowed_relationships = {
    'CONTAINS', 'DECLARES', 'HAS_FIELD', 'USES', 'CALLS', 'EXTENDS', 'IMPLEMENTS',
    'MAPS_TO', 'READS_FROM', 'WRITES_TO', 'FLOWS_TO', 'TRANSFORMS_TO', 'PRODUCES',
    'EXPOSES', 'ACCEPTS', 'RETURNS', 'INVOKES', 'PERSISTS_TO'
}

# Apply filters
df_final = df_final[
    (df_final['source_type'].isin(allowed_nodes)) &
    (df_final['destination_type'].isin(allowed_nodes)) &
    (df_final['relationship'].isin(allowed_relationships))
]


# Clean up column names and ensure consistency
required_columns = ['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship']
for col in required_columns:
    if col not in df_final.columns:
        df_final[col] = ''

# Final cleaning and validation
df_final = df_final[df_final['source_node'].notna() & (df_final['source_node'] != '')]
df_final = df_final[df_final['destination_node'].notna() & (df_final['destination_node'] != '')]

# Filter out self-referential relationships EXCEPT for valid File-Class DECLARES
file_class_declares = (
    (df_final['source_type'] == 'File') & 
    (df_final['destination_type'] == 'Class') & 
    (df_final['relationship'] == 'DECLARES')
)
df_final = df_final[
    (df_final['source_node'] != df_final['destination_node']) | file_class_declares
]

# Save to CSV
csv_filename = 'servicesbolt_lineage_v9.csv'
df_final[required_columns].to_csv(csv_filename, index=False)

print(f"\n✅ Stage 6 Complete: {len(df_final)} relationships consolidated")
print(f"📄 CSV saved: {csv_filename}")

# Summary by relationship type
relationship_summary = df_final['relationship'].value_counts()
print("\nRelationship breakdown:")
for rel_type, count in relationship_summary.items():
    print(f"  {rel_type}: {count}")

# ========== STAGE 7: NEO4J UPLOAD ==========

def upload_to_neo4j(df_final):
    """Upload relationships to Neo4j database with improved variable display"""
    # Collect unique nodes
    unique_nodes = set()
    for _, row in df_final.iterrows():
        unique_nodes.add((row['source_node'], row['source_type']))
        unique_nodes.add((row['destination_node'], row['destination_type']))
    
    print(f"Creating {len(unique_nodes)} unique nodes...")
    
    # Create nodes with proper variable display
    for node_name, node_type in tqdm(unique_nodes, desc="Creating nodes"):
        if node_type == 'Variable' and '.' in node_name:
            # For variables, show only the variable name, store context as property
            context_part, var_part = node_name.split('.', 1)
            create_query = f"MERGE (n:{node_type} {{name: '{var_part}', context: '{context_part}', full_name: '{node_name}'}})"
        else:
            # For other node types, use the full name
            create_query = f"MERGE (n:{node_type} {{name: '{node_name}'}})"
        
        try:
            graph.query(create_query)
        except Exception as e:
            print(f"Error creating node {node_name} ({node_type}): {e}")
    
    # Create relationships with proper variable matching
    for _, row in tqdm(df_final.iterrows(), desc="Creating relationships", total=len(df_final)):
        # Handle variable nodes differently
        source_match = f"{{full_name: '{row['source_node']}'}}" if row['source_type'] == 'Variable' and '.' in row['source_node'] else f"{{name: '{row['source_node']}'}}"
        target_match = f"{{full_name: '{row['destination_node']}'}}" if row['destination_type'] == 'Variable' and '.' in row['destination_node'] else f"{{name: '{row['destination_node']}'}}"
        
        create_rel_query = f"""
        MATCH (s:{row['source_type']} {source_match})
        MATCH (t:{row['destination_type']} {target_match})
        MERGE (s)-[:{row['relationship']}]->(t)
        """
        try:
            graph.query(create_rel_query)
        except Exception as e:
            print(f"Error creating relationship: {e}")
    
    print(f"✅ Neo4j upload complete: {len(unique_nodes)} nodes, {len(df_final)} relationships")


# Execute Neo4j upload
upload_to_neo4j(df_final)

# Final memory save
save_memory(memory)

print("\n========== PIPELINE COMPLETE ==========")
print(f"✅ Stage 7 Complete: Data uploaded to Neo4j")
print(f"📊 Final relationships: {len(df_final)}")
print(f"💾 Memory file: {MEMORY_FILE}")
print(f"📄 CSV output: java_lineage_v9.csv")
print(f"🗄️ Neo4j database: {NEO4J_DB}")
print("========================================")
