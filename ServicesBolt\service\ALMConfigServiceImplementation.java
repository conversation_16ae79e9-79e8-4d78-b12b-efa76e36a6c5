/**
 * 
 */
package com.bolt.dashboard.service;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.core.repository.ALMConfigRepo;
import com.bolt.dashboard.response.DataResponse;

/**
 * <AUTHOR>
 *
 */
@Service
public class ALMConfigServiceImplementation implements ALMConfigService {
	private ALMConfigRepo almConfigRepo;
	private static final Logger LOG = LogManager.getLogger(ALMConfigServiceImplementation.class);

	@Autowired
	public ALMConfigServiceImplementation(ALMConfigRepo repo) {
		this.almConfigRepo = repo;
	}

	@Override
//	@CacheEvict(value="retrieveALMConfig", key ="'retrieveALMConfig'+#req.getProjectName()", cacheManager="timeoutCacheManager")
	public ALMConfiguration saveALMConfig(ALMConfiguration req) {

		if (almConfigRepo.findByProjectName(req.getProjectName()) != null) {
			almConfigRepo.deleteByProjectName(req.getProjectName());
		}
		LOG.info("ALM Configuration saved successfully...");
		return almConfigRepo.save(req);

	}

	@Override
//	@Cacheable(value="retrieveALMConfig", key ="'retrieveALMConfig'+#projectName", cacheManager="timeoutCacheManager")
	public DataResponse<ALMConfiguration> retrieveALMConfig(String projectName) {
		long lastUpdate = 1;
		ALMConfiguration result = almConfigRepo.findByProjectName(projectName).get(0);

		return new DataResponse<ALMConfiguration>(result, lastUpdate);
	}

}
