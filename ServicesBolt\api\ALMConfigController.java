/**
 * 
 */
package com.bolt.dashboard.api;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

import org.apache.logging.log4j.LogManager;
 import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.bolt.dashboard.core.model.ALMConfiguration;
import com.bolt.dashboard.request.ALMConfigReq;
import com.bolt.dashboard.response.DataResponse;
import com.bolt.dashboard.service.ALMConfigService;


@RestController
public class ALMConfigController {
	private static final Logger LOG = LogManager.getLogger(ALMConfigController.class);
	@Autowired
	private ALMConfigService almConfigService;

	@Autowired
	public ALMConfigController(ALMConfigService almConfigService) {
		this.almConfigService = almConfigService;
	}
		

		@RequestMapping(value = "/almconfig", method = POST, consumes = APPLICATION_JSON_VALUE, produces = APPLICATION_JSON_VALUE)
		public ResponseEntity<ALMConfiguration> saveALMConfig(@RequestBody ALMConfigReq req) {
			//ALMConfigReq almConfigReq = req;

			LOG.info("Inside ALMConfigController and org  :  " + req.getProjectName());
			return ResponseEntity.status(201).body(almConfigService.saveALMConfig(req.toDetailsAddSetting(req)));
	}

	@RequestMapping(value = "/almconfigDetails", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<ALMConfiguration> retrieveList(String projectName) {
		return almConfigService.retrieveALMConfig(projectName);

	}
	
	
	@RequestMapping(value = "/almconfigDetailsConfig", method = GET, produces = APPLICATION_JSON_VALUE)
	public DataResponse<ALMConfiguration> retrieveAlmConfig(String projectName) {
		return almConfigService.retrieveALMConfig(projectName);

	}

}
