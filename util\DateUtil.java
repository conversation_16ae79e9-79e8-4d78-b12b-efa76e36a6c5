package com.bolt.dashboard.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

@Component
public class DateUtil {

    public String getDateInFormat(String format, Date dateInput) {
        String pattern = format;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

        return simpleDateFormat.format(dateInput);

    }

    public Map<String, String> getLastWeekWorkingDateRange() {
        Map<String, String> map = new HashMap<>();
        Date date = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.setFirstDayOfWeek(2);// set Monday as First day of the week
        int i = c.get(Calendar.DAY_OF_WEEK) - c.getFirstDayOfWeek();
        c.add(Calendar.DATE, -i - 7);
        Date start = c.getTime();
        c.add(Calendar.DATE, 4);
        Date end = c.getTime();
        map.put("start", getDateInFormat("yyyy/MM/dd", start));
        map.put("end", getDateInFormat("yyyy/MM/dd", end));
        return map;

    }

}
