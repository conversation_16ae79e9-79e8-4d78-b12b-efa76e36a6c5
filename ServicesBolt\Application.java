/**
 * 
 */
package com.bolt.dashboard;

import org.quartz.SchedulerException;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.web.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import com.bolt.dashboard.config.RestApiConfig;
import com.bolt.dashboard.config.WebMVCConfig;
import com.bolt.dashboard.core.config.DataConfig;

@SpringBootApplication
@EnableCaching
public class Application extends SpringBootServletInitializer {
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(Application.class, RestApiConfig.class, WebMVCConfig.class, DataConfig.class);
    }
    @Bean
	BCryptPasswordEncoder passwordEncoder(){
		return new BCryptPasswordEncoder();
	}
    public static void main(String[] args) throws SchedulerException {

        new Application().configure(new SpringApplicationBuilder(Application.class)).run(args);
        new TriggerCollector().getDataFromTools(true);
    }
}
